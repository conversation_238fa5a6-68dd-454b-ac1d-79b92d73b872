"""
MIR-Flickr综合评估框架 - 参考MM-IMDB测试流程
包括对比试验、消融实验和KG有效性测试
Comprehensive MIR-Flickr evaluation framework following MM-IMDB testing procedures
"""

import os
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import json
import logging
from torch.utils.data import DataLoader
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import modules
from utils.mirflickr_25k_dataset import MIRFlickr25KDataset
from models.kg_disentangle_net import KGDisentangleNet
from models.base_model import BaseModel
from utils.losses import compute_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# MIR-Flickr concept names
MIRFLICKR_CONCEPTS = [
    'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 'flower', 
    'food', 'indoor', 'lake', 'male', 'night', 'people', 'plant_life', 
    'portrait', 'river', 'sea', 'sky', 'structures', 'sunset', 'transport', 
    'tree', 'water'
]

class BaselineModel(BaseModel):
    """基线模型 - 不使用KG和解耦模块"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(BaselineModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,  # 保持接口一致
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )
        
        # 简单的融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        
        # 简单拼接融合
        fused = torch.cat([text_encoded, visual_encoded], dim=-1)
        logits = self.fusion(fused)
        
        # 返回格式与主模型一致
        dummy_loss = torch.tensor(0.0, device=logits.device)
        return logits, dummy_loss

class NoKGModel(BaseModel):
    """无KG模型 - 不使用知识图谱"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(NoKGModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )
        
        # 导入解耦模块但不使用KG
        from models.kg_disentangle_net import RedundancyDetectionModule, AdaptiveFusionModule
        
        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)
        
        # 分类器
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = self.kg_encoder(kg_features) if kg_features is not None else torch.zeros_like(text_encoded)
        
        # 冗余检测
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        redundancy_score, _ = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        redundancy_score = redundancy_score.squeeze(1)
        
        # 自适应融合（不使用KG特征）
        fused = self.adaptive_fusion(text_encoded, visual_encoded, torch.zeros_like(text_encoded), redundancy_score)
        
        # 分类
        logits = self.enhanced_classifier(fused)
        
        # 计算解耦损失
        text_invariant = text_encoded * redundancy_score
        visual_invariant = visual_encoded * redundancy_score
        text_specific = text_encoded * (1 - redundancy_score)
        visual_specific = visual_encoded * (1 - redundancy_score)
        
        invariant_sim = torch.nn.functional.cosine_similarity(text_invariant, visual_invariant, dim=1).mean()
        specific_sim = torch.nn.functional.cosine_similarity(text_specific, visual_specific, dim=1).mean()
        
        disentanglement_loss = -0.5 * invariant_sim + 0.3 * specific_sim
        
        return logits, disentanglement_loss

def load_model_variants(model_path, device):
    """加载不同的模型变体"""
    models = {}
    
    # 1. 完整模型 (KG + Disentanglement)
    logger.info("Loading full KG-Disentangle model...")
    full_model = KGDisentangleNet(
        text_dim=300, visual_dim=4096, kg_dim=200, 
        hidden_dim=256, num_classes=24
    )
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    full_model.load_state_dict(checkpoint['model_state_dict'])
    full_model.to(device).eval()
    models['Full_KG_Disentangle'] = full_model
    
    # 2. 基线模型 (Simple Fusion)
    logger.info("Creating baseline model...")
    baseline_model = BaselineModel(
        text_dim=300, visual_dim=4096, 
        hidden_dim=256, num_classes=24
    )
    # 复制编码器权重
    baseline_model.text_encoder.load_state_dict(full_model.text_encoder.state_dict())
    baseline_model.visual_encoder.load_state_dict(full_model.visual_encoder.state_dict())
    baseline_model.to(device).eval()
    models['Baseline_Simple_Fusion'] = baseline_model
    
    # 3. 无KG模型 (Disentanglement only)
    logger.info("Creating no-KG model...")
    no_kg_model = NoKGModel(
        text_dim=300, visual_dim=4096, 
        hidden_dim=256, num_classes=24
    )
    # 复制编码器权重
    no_kg_model.text_encoder.load_state_dict(full_model.text_encoder.state_dict())
    no_kg_model.visual_encoder.load_state_dict(full_model.visual_encoder.state_dict())
    no_kg_model.kg_encoder.load_state_dict(full_model.kg_encoder.state_dict())
    no_kg_model.to(device).eval()
    models['No_KG_Disentangle_Only'] = no_kg_model
    
    return models

def evaluate_model(model, test_loader, device, model_name):
    """评估单个模型"""
    logger.info(f"Evaluating {model_name}...")
    
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Testing {model_name}"):
            try:
                # 准备输入
                image = batch['image'].to(device)
                text = batch['text'].to(device)
                labels = batch['labels'].to(device)
                kg_features = batch['kg_features'].to(device)
                label_embeddings = batch['label_embeddings'].to(device)
                
                # 前向传播
                outputs = model(image, text, kg_features, label_embeddings)
                logits = outputs[0]
                
                # 获取预测
                preds = torch.sigmoid(logits).detach().cpu().numpy()
                
                all_preds.append(preds)
                all_labels.append(labels.detach().cpu().numpy())
                
            except Exception as e:
                logger.warning(f"Error in batch for {model_name}: {e}")
                continue
    
    if all_preds:
        all_preds = np.concatenate(all_preds, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)
        
        # 计算指标
        metrics = compute_enhanced_metrics(all_labels, all_preds)
        metrics['model_name'] = model_name
        
        logger.info(f"{model_name} - F1: {metrics['f1']:.4f}, mAP: {metrics['mAP']:.4f}")
        return metrics, all_preds, all_labels
    else:
        logger.error(f"No valid predictions for {model_name}")
        return None, None, None

def compute_enhanced_metrics(labels, preds, threshold=0.5):
    """计算增强指标 - 参考MM-IMDB"""
    binary_preds = (preds > threshold).astype(int)
    
    # 样本平均指标
    precision, recall, f1, _ = precision_recall_fscore_support(
        labels, binary_preds, average='samples', zero_division=0
    )
    
    # 微平均指标
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        labels, binary_preds, average='micro', zero_division=0
    )
    
    # 宏平均指标
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        labels, binary_preds, average='macro', zero_division=0
    )
    
    # 每类指标
    per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
        labels, binary_preds, average=None, zero_division=0
    )
    
    # mAP
    mAP_scores = []
    for i in range(labels.shape[1]):
        if np.sum(labels[:, i]) > 0:
            try:
                ap = average_precision_score(labels[:, i], preds[:, i])
                mAP_scores.append(ap)
            except:
                mAP_scores.append(0.0)
        else:
            mAP_scores.append(0.0)
    
    mAP = np.mean(mAP_scores)
    
    # Hamming准确率
    hamming_accuracy = 1.0 - np.mean(np.abs(labels - binary_preds))
    
    return {
        'precision': float(precision),
        'recall': float(recall),
        'f1': float(f1),
        'f1_micro': float(f1_micro),
        'f1_macro': float(f1_macro),
        'precision_micro': float(precision_micro),
        'precision_macro': float(precision_macro),
        'recall_micro': float(recall_micro),
        'recall_macro': float(recall_macro),
        'hamming_accuracy': float(hamming_accuracy),
        'mAP': float(mAP),
        'per_class_precision': per_class_precision.tolist(),
        'per_class_recall': per_class_recall.tolist(),
        'per_class_f1': per_class_f1.tolist(),
        'per_class_mAP': mAP_scores
    }

def generate_comparison_visualizations(all_metrics, output_dir):
    """生成对比可视化"""
    logger.info("Generating comparison visualizations...")
    
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # 1. 模型性能对比
    plt.figure(figsize=(15, 10))
    
    models = list(all_metrics.keys())
    metrics_names = ['F1', 'F1-Micro', 'F1-Macro', 'Precision', 'Recall', 'mAP', 'Hamming Acc']
    
    x = np.arange(len(metrics_names))
    width = 0.25
    
    for i, model in enumerate(models):
        values = [
            all_metrics[model]['f1'],
            all_metrics[model]['f1_micro'],
            all_metrics[model]['f1_macro'],
            all_metrics[model]['precision'],
            all_metrics[model]['recall'],
            all_metrics[model]['mAP'],
            all_metrics[model]['hamming_accuracy']
        ]
        
        plt.bar(x + i * width, values, width, label=model, alpha=0.8)
    
    plt.xlabel('Metrics', fontsize=12)
    plt.ylabel('Score', fontsize=12)
    plt.title('MIR-Flickr Model Comparison - Overall Performance', fontsize=14, fontweight='bold')
    plt.xticks(x + width, metrics_names, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.ylim(0, 1)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'model_comparison_overall.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. F1分数详细对比
    plt.figure(figsize=(12, 8))
    
    f1_scores = [all_metrics[model]['f1'] for model in models]
    colors = plt.cm.Set3(np.linspace(0, 1, len(models)))
    
    bars = plt.bar(models, f1_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    plt.ylabel('F1 Score', fontsize=12)
    plt.title('MIR-Flickr Model Comparison - F1 Scores', fontsize=14, fontweight='bold')
    plt.ylim(0, 1)
    plt.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar, score in zip(bars, f1_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'model_comparison_f1.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Comparison visualizations saved to {vis_dir}")

def save_comprehensive_results(all_metrics, output_dir):
    """保存综合结果"""
    logger.info("Saving comprehensive results...")
    
    results_dir = os.path.join(output_dir, 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存所有模型指标
    with open(os.path.join(results_dir, 'all_models_metrics.json'), 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    # 创建对比表格
    comparison_table = {
        'model_comparison': {
            'metrics': ['F1', 'F1-Micro', 'F1-Macro', 'Precision', 'Recall', 'mAP', 'Hamming Accuracy'],
            'models': {}
        }
    }
    
    for model_name, metrics in all_metrics.items():
        comparison_table['model_comparison']['models'][model_name] = [
            metrics['f1'],
            metrics['f1_micro'],
            metrics['f1_macro'],
            metrics['precision'],
            metrics['recall'],
            metrics['mAP'],
            metrics['hamming_accuracy']
        ]
    
    with open(os.path.join(results_dir, 'model_comparison_table.json'), 'w') as f:
        json.dump(comparison_table, f, indent=2)
    
    # 保存实验总结
    experiment_summary = {
        'experiment_info': {
            'dataset': 'MIR-Flickr-25K',
            'test_timestamp': datetime.now().isoformat(),
            'models_tested': list(all_metrics.keys()),
            'best_model': max(all_metrics.keys(), key=lambda x: all_metrics[x]['f1']),
            'best_f1': max(all_metrics[x]['f1'] for x in all_metrics.keys())
        },
        'ablation_analysis': {
            'kg_effectiveness': 'To be computed',
            'disentanglement_effectiveness': 'To be computed'
        }
    }
    
    with open(os.path.join(results_dir, 'experiment_summary.json'), 'w') as f:
        json.dump(experiment_summary, f, indent=2)
    
    logger.info(f"Comprehensive results saved to {results_dir}")

def main():
    """主评估函数"""
    # 配置
    model_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/cpu_model.pth'
    data_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr'
    kg_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data'
    output_dir = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/comprehensive_evaluation_results'
    batch_size = 8
    device = 'cpu'
    
    logger.info("=" * 80)
    logger.info("MIR-FLICKR COMPREHENSIVE EVALUATION")
    logger.info("=" * 80)
    logger.info("包括: 对比试验、消融实验、KG有效性测试")
    logger.info(f"模型路径: {model_path}")
    logger.info(f"输出目录: {output_dir}")
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{output_dir}_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查模型文件
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return
    
    # 加载模型变体
    models = load_model_variants(model_path, device)
    
    # 创建测试数据集
    logger.info("创建测试数据集...")
    test_dataset = MIRFlickr25KDataset(
        data_path=data_path,
        kg_path=kg_path,
        mode='test'
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=False
    )
    
    logger.info(f"测试数据集大小: {len(test_dataset)}")
    
    # 评估所有模型
    all_metrics = {}
    
    for model_name, model in models.items():
        metrics, preds, labels = evaluate_model(model, test_loader, device, model_name)
        if metrics is not None:
            all_metrics[model_name] = metrics
    
    # 输出结果
    logger.info("=" * 80)
    logger.info("评估结果总结")
    logger.info("=" * 80)
    
    for model_name, metrics in all_metrics.items():
        logger.info(f"{model_name}:")
        logger.info(f"  F1: {metrics['f1']:.4f}")
        logger.info(f"  mAP: {metrics['mAP']:.4f}")
        logger.info(f"  Precision: {metrics['precision']:.4f}")
        logger.info(f"  Recall: {metrics['recall']:.4f}")
        logger.info("-" * 40)
    
    # 生成可视化和保存结果
    generate_comparison_visualizations(all_metrics, output_dir)
    save_comprehensive_results(all_metrics, output_dir)
    
    logger.info("综合评估完成!")
    logger.info(f"所有结果保存到: {output_dir}")

if __name__ == "__main__":
    main()
