"""
MIR-Flickr消融实验脚本 - 修复版本
Fixed MIR-Flickr Ablation Experiments with dimension and batch error fixes
"""

import os
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import json
import logging
from torch.utils.data import DataLoader
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import modules
from utils.mirflickr_25k_dataset import MIRFlickr25KDataset
from models.kg_disentangle_net import KGDisentangleNet
from models.base_model import BaseModel

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# MIR-Flickr concept names
MIRFLICKR_CONCEPTS = [
    'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 'flower',
    'food', 'indoor', 'lake', 'male', 'night', 'people', 'plant_life',
    'portrait', 'river', 'sea', 'sky', 'structures', 'sunset', 'transport',
    'tree', 'water'
]

class BaselineModel(BaseModel):
    """基线模型 - 简单的多模态融合"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(BaselineModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # 简单融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)

        # 简单拼接融合
        fused = torch.cat([text_encoded, visual_encoded], dim=-1)
        logits = self.fusion(fused)

        # 返回格式与主模型一致
        dummy_loss = torch.tensor(0.0, device=logits.device)
        return logits, dummy_loss

class NoKGModel(BaseModel):
    """无KG模型 - 只使用解耦机制"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(NoKGModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # 导入解耦模块
        from models.kg_disentangle_net import RedundancyDetectionModule, AdaptiveFusionModule

        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)

        # 分类器
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = torch.zeros_like(text_encoded)  # 零KG特征

        # 冗余检测
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        redundancy_score, _ = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        redundancy_score = redundancy_score.squeeze(1)

        # 自适应融合（不使用KG特征）
        fused = self.adaptive_fusion(text_encoded, visual_encoded, kg_encoded, redundancy_score)

        # 分类
        logits = self.enhanced_classifier(fused)

        # 计算解耦损失
        text_invariant = text_encoded * redundancy_score
        visual_invariant = visual_encoded * redundancy_score
        text_specific = text_encoded * (1 - redundancy_score)
        visual_specific = visual_encoded * (1 - redundancy_score)

        invariant_sim = torch.nn.functional.cosine_similarity(text_invariant, visual_invariant, dim=1).mean()
        specific_sim = torch.nn.functional.cosine_similarity(text_specific, visual_specific, dim=1).mean()

        disentanglement_loss = -0.5 * invariant_sim + 0.3 * specific_sim

        return logits, disentanglement_loss

class NoRedundancyModel(BaseModel):
    """无冗余检测模型 - 修复版本"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(NoRedundancyModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # 导入图推理模块
        from models.kg_disentangle_net import GraphReasoningModule

        self.graph_reasoner = GraphReasoningModule(hidden_dim, 200)

        # 简单融合（不使用冗余抑制）
        self.simple_fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # 分类器
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = self.kg_encoder(kg_features) if kg_features is not None else torch.zeros_like(text_encoded)

        # 图推理（不使用冗余检测）
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        kg_features_3d = kg_features.unsqueeze(1) if kg_features is not None else torch.zeros_like(text_encoded_3d)

        text_refined_3d = self.graph_reasoner(text_encoded_3d, kg_features_3d)
        visual_refined_3d = self.graph_reasoner(visual_encoded_3d, kg_features_3d)

        text_refined = text_refined_3d.squeeze(1)
        visual_refined = visual_refined_3d.squeeze(1)

        # 简单融合（不使用冗余抑制）
        fused = self.simple_fusion(torch.cat([text_refined, visual_refined, kg_encoded], dim=-1))

        # 分类
        logits = self.enhanced_classifier(fused)

        # 计算简单的解耦损失
        disentanglement_loss = torch.tensor(0.0, device=logits.device)

        return logits, disentanglement_loss

class NoGraphReasoningModel(BaseModel):
    """无图推理模型 - 修复版本"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(NoGraphReasoningModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # 导入冗余检测和融合模块
        from models.kg_disentangle_net import RedundancyDetectionModule, AdaptiveFusionModule

        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)

        # 分类器
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = self.kg_encoder(kg_features) if kg_features is not None else torch.zeros_like(text_encoded)

        # 冗余检测
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        redundancy_score, _ = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        redundancy_score = redundancy_score.squeeze(1)

        # 不使用图推理，直接使用编码特征
        text_refined = text_encoded
        visual_refined = visual_encoded

        # 自适应融合
        fused = self.adaptive_fusion(text_refined, visual_refined, kg_encoded, redundancy_score)

        # 分类
        logits = self.enhanced_classifier(fused)

        # 计算解耦损失
        text_invariant = text_refined * redundancy_score
        visual_invariant = visual_refined * redundancy_score
        text_specific = text_refined * (1 - redundancy_score)
        visual_specific = visual_refined * (1 - redundancy_score)

        invariant_sim = torch.nn.functional.cosine_similarity(text_invariant, visual_invariant, dim=1).mean()
        specific_sim = torch.nn.functional.cosine_similarity(text_specific, visual_specific, dim=1).mean()

        disentanglement_loss = -0.5 * invariant_sim + 0.3 * specific_sim

        return logits, disentanglement_loss

class NoAdaptiveFusionModel(BaseModel):
    """无自适应融合模型 - 修复版本"""
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=256, num_classes=24):
        super(NoAdaptiveFusionModel, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=200,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # 导入部分模块
        from models.kg_disentangle_net import RedundancyDetectionModule, GraphReasoningModule

        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)
        self.graph_reasoner = GraphReasoningModule(hidden_dim, 200)

        # 简单融合
        self.simple_fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # 分类器
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, images, texts, kg_features=None, label_embeddings=None):
        # 编码特征
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = self.kg_encoder(kg_features) if kg_features is not None else torch.zeros_like(text_encoded)

        # 冗余检测
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        redundancy_score, _ = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        redundancy_score = redundancy_score.squeeze(1)

        # 图推理
        kg_features_3d = kg_features.unsqueeze(1) if kg_features is not None else torch.zeros_like(text_encoded_3d)
        text_refined_3d = self.graph_reasoner(text_encoded_3d, kg_features_3d)
        visual_refined_3d = self.graph_reasoner(visual_encoded_3d, kg_features_3d)

        text_refined = text_refined_3d.squeeze(1)
        visual_refined = visual_refined_3d.squeeze(1)

        # 简单融合（不使用自适应机制）
        fused = self.simple_fusion(torch.cat([text_refined, visual_refined, kg_encoded], dim=-1))

        # 分类
        logits = self.enhanced_classifier(fused)

        # 计算解耦损失
        text_invariant = text_refined * redundancy_score
        visual_invariant = visual_refined * redundancy_score
        text_specific = text_refined * (1 - redundancy_score)
        visual_specific = visual_refined * (1 - redundancy_score)

        invariant_sim = torch.nn.functional.cosine_similarity(text_invariant, visual_invariant, dim=1).mean()
        specific_sim = torch.nn.functional.cosine_similarity(text_specific, visual_specific, dim=1).mean()

        disentanglement_loss = -0.5 * invariant_sim + 0.3 * specific_sim

        return logits, disentanglement_loss

def load_trained_weights_safe(model, model_path, device):
    """安全地加载训练好的权重到消融模型"""
    logger.info(f"Loading weights from {model_path}")

    try:
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        full_state_dict = checkpoint['model_state_dict']

        # 获取模型的state_dict
        model_state_dict = model.state_dict()

        # 只加载匹配的权重
        matched_weights = {}
        skipped_weights = []

        for name, param in model_state_dict.items():
            if name in full_state_dict:
                if param.shape == full_state_dict[name].shape:
                    matched_weights[name] = full_state_dict[name]
                    logger.debug(f"Loaded weight: {name} {param.shape}")
                else:
                    skipped_weights.append(f"{name}: {param.shape} vs {full_state_dict[name].shape}")
                    logger.debug(f"Shape mismatch for {name}: {param.shape} vs {full_state_dict[name].shape}")
            else:
                skipped_weights.append(f"{name}: not found in checkpoint")
                logger.debug(f"Weight not found: {name}")

        # 加载匹配的权重
        model_state_dict.update(matched_weights)
        model.load_state_dict(model_state_dict)

        logger.info(f"Loaded {len(matched_weights)}/{len(model_state_dict)} weights")
        if skipped_weights:
            logger.info(f"Skipped {len(skipped_weights)} weights due to mismatch or absence")

        return model

    except Exception as e:
        logger.error(f"Error loading weights: {e}")
        logger.info("Continuing with randomly initialized weights")
        return model

def create_fixed_ablation_models(model_path, device):
    """创建修复版本的消融实验模型"""
    models = {}

    # 1. 完整模型 (Full Model)
    logger.info("Creating full model...")
    full_model = KGDisentangleNet(
        text_dim=300, visual_dim=4096, kg_dim=200,
        hidden_dim=256, num_classes=24
    )
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    full_model.load_state_dict(checkpoint['model_state_dict'])
    full_model.to(device).eval()
    models['Full_Model'] = full_model

    # 2. 基线模型 (Baseline - Simple Fusion)
    logger.info("Creating baseline model...")
    baseline_model = BaselineModel(
        text_dim=300, visual_dim=4096,
        hidden_dim=256, num_classes=24
    )
    baseline_model = load_trained_weights_safe(baseline_model, model_path, device)
    baseline_model.to(device).eval()
    models['Baseline_Simple_Fusion'] = baseline_model

    # 3. 无KG模型 (No Knowledge Graph)
    logger.info("Creating no-KG model...")
    no_kg_model = NoKGModel(
        text_dim=300, visual_dim=4096,
        hidden_dim=256, num_classes=24
    )
    no_kg_model = load_trained_weights_safe(no_kg_model, model_path, device)
    no_kg_model.to(device).eval()
    models['No_Knowledge_Graph'] = no_kg_model

    # 4. 无冗余检测模型 (No Redundancy Detection) - 修复版本
    logger.info("Creating no-redundancy model...")
    no_redundancy_model = NoRedundancyModel(
        text_dim=300, visual_dim=4096,
        hidden_dim=256, num_classes=24
    )
    no_redundancy_model = load_trained_weights_safe(no_redundancy_model, model_path, device)
    no_redundancy_model.to(device).eval()
    models['No_Redundancy_Detection'] = no_redundancy_model

    # 5. 无图推理模型 (No Graph Reasoning) - 修复版本
    logger.info("Creating no-graph-reasoning model...")
    no_graph_model = NoGraphReasoningModel(
        text_dim=300, visual_dim=4096,
        hidden_dim=256, num_classes=24
    )
    no_graph_model = load_trained_weights_safe(no_graph_model, model_path, device)
    no_graph_model.to(device).eval()
    models['No_Graph_Reasoning'] = no_graph_model

    # 6. 无自适应融合模型 (No Adaptive Fusion) - 修复版本
    logger.info("Creating no-adaptive-fusion model...")
    no_fusion_model = NoAdaptiveFusionModel(
        text_dim=300, visual_dim=4096,
        hidden_dim=256, num_classes=24
    )
    no_fusion_model = load_trained_weights_safe(no_fusion_model, model_path, device)
    no_fusion_model.to(device).eval()
    models['No_Adaptive_Fusion'] = no_fusion_model

    return models

def evaluate_ablation_model_safe(model, test_loader, device, model_name):
    """安全地评估单个消融模型"""
    logger.info(f"Evaluating {model_name}...")

    model.eval()
    all_preds = []
    all_labels = []
    error_count = 0

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(test_loader, desc=f"Testing {model_name}")):
            try:
                # 准备输入
                image = batch['image'].to(device)
                text = batch['text'].to(device)
                labels = batch['labels'].to(device)
                kg_features = batch['kg_features'].to(device)
                label_embeddings = batch['label_embeddings'].to(device)

                # 前向传播 - 处理不同的返回格式
                outputs = model(image, text, kg_features, label_embeddings)

                # 统一处理返回格式
                if isinstance(outputs, tuple):
                    logits = outputs[0]
                elif isinstance(outputs, dict):
                    logits = outputs['logits']
                else:
                    logits = outputs

                # 检查logits维度
                if logits.dim() != 2 or logits.size(1) != 24:
                    logger.warning(f"Unexpected logits shape: {logits.shape} in batch {batch_idx}")
                    continue

                # 获取预测
                preds = torch.sigmoid(logits).detach().cpu().numpy()

                all_preds.append(preds)
                all_labels.append(labels.detach().cpu().numpy())

            except Exception as e:
                error_count += 1
                logger.warning(f"Error in batch {batch_idx} for {model_name}: {e}")
                if error_count > 10:  # 如果错误太多，停止评估
                    logger.error(f"Too many errors ({error_count}) for {model_name}, stopping evaluation")
                    break
                continue

    if all_preds:
        all_preds = np.concatenate(all_preds, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)

        # 计算指标
        metrics = compute_ablation_metrics(all_labels, all_preds)
        metrics['model_name'] = model_name
        metrics['error_count'] = error_count

        logger.info(f"{model_name} - F1: {metrics['f1']:.4f}, mAP: {metrics['mAP']:.4f}, Errors: {error_count}")
        return metrics
    else:
        logger.error(f"No valid predictions for {model_name}")
        return None

def compute_ablation_metrics(labels, preds, threshold=0.5):
    """计算消融实验指标 - 与MM-IMDB一致"""
    binary_preds = (preds > threshold).astype(int)

    # 样本平均指标
    precision, recall, f1, _ = precision_recall_fscore_support(
        labels, binary_preds, average='samples', zero_division=0
    )

    # 微平均指标
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        labels, binary_preds, average='micro', zero_division=0
    )

    # 宏平均指标
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        labels, binary_preds, average='macro', zero_division=0
    )

    # mAP
    mAP_scores = []
    for i in range(labels.shape[1]):
        if np.sum(labels[:, i]) > 0:
            try:
                ap = average_precision_score(labels[:, i], preds[:, i])
                mAP_scores.append(ap)
            except:
                mAP_scores.append(0.0)
        else:
            mAP_scores.append(0.0)

    mAP = np.mean(mAP_scores)

    # Hamming准确率
    hamming_accuracy = 1.0 - np.mean(np.abs(labels - binary_preds))

    return {
        'precision': float(precision),
        'recall': float(recall),
        'f1': float(f1),
        'f1_micro': float(f1_micro),
        'f1_macro': float(f1_macro),
        'precision_micro': float(precision_micro),
        'precision_macro': float(precision_macro),
        'recall_micro': float(recall_micro),
        'recall_macro': float(recall_macro),
        'hamming_accuracy': float(hamming_accuracy),
        'mAP': float(mAP)
    }

def save_fixed_ablation_results(all_metrics, output_dir):
    """保存修复版本的消融实验结果"""
    logger.info("Saving fixed ablation study results...")

    results_dir = os.path.join(output_dir, 'results')
    os.makedirs(results_dir, exist_ok=True)

    # 转换numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.float32, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.int32, np.int64)):
            return int(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        else:
            return obj

    # 保存所有指标
    all_metrics_serializable = convert_numpy_types(all_metrics)
    with open(os.path.join(results_dir, 'fixed_ablation_all_metrics.json'), 'w') as f:
        json.dump(all_metrics_serializable, f, indent=2)

    # 创建对比表格
    comparison_data = {
        'models': list(all_metrics.keys()),
        'f1': [all_metrics[model]['f1'] for model in all_metrics.keys()],
        'f1_micro': [all_metrics[model]['f1_micro'] for model in all_metrics.keys()],
        'f1_macro': [all_metrics[model]['f1_macro'] for model in all_metrics.keys()],
        'mAP': [all_metrics[model]['mAP'] for model in all_metrics.keys()],
        'precision': [all_metrics[model]['precision'] for model in all_metrics.keys()],
        'recall': [all_metrics[model]['recall'] for model in all_metrics.keys()],
        'hamming_accuracy': [all_metrics[model]['hamming_accuracy'] for model in all_metrics.keys()]
    }

    with open(os.path.join(results_dir, 'fixed_ablation_comparison_table.json'), 'w') as f:
        json.dump(comparison_data, f, indent=2)

    logger.info(f"Fixed ablation study results saved to {results_dir}")

def main():
    """主消融实验函数 - 修复版本"""
    # 配置
    model_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/cpu_model.pth'
    data_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mirflickr'
    kg_path = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data'
    output_dir = '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/mirflickr_ablation_study_fixed'
    batch_size = 4  # 减小batch size以避免内存问题
    device = 'cpu'

    logger.info("=" * 80)
    logger.info("MIR-FLICKR ABLATION STUDY - FIXED VERSION")
    logger.info("=" * 80)
    logger.info("修复维度不匹配和batch error问题的消融实验")

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{output_dir}_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)

    # 检查模型文件
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return

    # 创建修复版本的消融模型
    models = create_fixed_ablation_models(model_path, device)

    # 创建测试数据集
    logger.info("创建测试数据集...")
    test_dataset = MIRFlickr25KDataset(
        data_path=data_path,
        kg_path=kg_path,
        mode='test'
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=2,  # 减少worker数量
        pin_memory=False
    )

    logger.info(f"测试数据集大小: {len(test_dataset)}")

    # 评估所有模型
    all_metrics = {}

    for model_name, model in models.items():
        logger.info(f"开始评估模型: {model_name}")
        metrics = evaluate_ablation_model_safe(model, test_loader, device, model_name)
        if metrics is not None:
            all_metrics[model_name] = metrics
        else:
            logger.warning(f"模型 {model_name} 评估失败")

    # 输出结果总结
    logger.info("=" * 80)
    logger.info("修复版本消融实验结果总结")
    logger.info("=" * 80)

    for model_name, metrics in all_metrics.items():
        logger.info(f"{model_name}:")
        logger.info(f"  F1: {metrics['f1']:.4f}")
        logger.info(f"  mAP: {metrics['mAP']:.4f}")
        logger.info(f"  Precision: {metrics['precision']:.4f}")
        logger.info(f"  Recall: {metrics['recall']:.4f}")
        logger.info(f"  Errors: {metrics.get('error_count', 0)}")
        logger.info("-" * 40)

    # 保存结果
    save_fixed_ablation_results(all_metrics, output_dir)

    logger.info("修复版本消融实验完成!")
    logger.info(f"所有结果保存到: {output_dir}")

if __name__ == "__main__":
    main()