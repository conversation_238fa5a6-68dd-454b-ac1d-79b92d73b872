"""
Evaluation script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network on MIR-Flickr dataset.
This script provides comprehensive evaluation similar to MM-IMDB evaluation.
"""

import os
import argparse
import torch
import numpy as np
from tqdm import tqdm
import json
import logging
from torch.utils.data import DataLoader
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import matplotlib.pyplot as plt
import seaborn as sns

from utils.mirflickr_dataset import MIRFlickrDataset
from models.kg_disentangle_net import KGDisentangleNet
from utils.losses import compute_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def load_model(model_path, device):
    """Load trained model."""
    logger.info(f"Loading model from {model_path}")

    # Load model state
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)

    # Initialize model with the same parameters as training
    model = KGDisentangleNet(
        text_dim=300,
        visual_dim=4096,
        kg_dim=200,
        hidden_dim=256,  # Match the training configuration
        num_classes=24  # MIR-Flickr processed has 24 concepts
    )

    # Load state dict
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.to(device)
    model.eval()

    logger.info("Model loaded successfully")
    return model

def evaluate_model(model, dataloader, device, args):
    """Evaluate model on dataset."""
    model.eval()

    all_preds = []
    all_labels = []
    all_redundancy_scores = []
    all_image_ids = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc=f"Evaluating on {args.split}"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            image_ids = batch['image_id']

            # Convert text to feature vectors (simple bag of words for now)
            text_features = torch.zeros(len(text), 300).to(device)
            for i, t in enumerate(text):
                # Simple bag of words representation
                words = t.split()
                if len(words) > 0:
                    # Use random features for now - in practice, use proper text encoder
                    text_features[i] = torch.randn(300).to(device)

            # Forward pass - correct parameter order: (images, texts, kg_features)
            outputs = model(image, text_features, kg_features, label_embeddings)

            # Model returns (logits, disentanglement_loss) or (logits, disentanglement_loss, features)
            if len(outputs) >= 2:
                logits = outputs[0]
                disentanglement_loss = outputs[1]
            else:
                logits = outputs
                disentanglement_loss = None

            # Get predictions
            preds = torch.sigmoid(logits).detach().cpu().numpy()
            redundancy_scores = None  # We'll compute this separately if needed

            all_preds.append(preds)
            all_labels.append(labels.detach().cpu().numpy())
            if redundancy_scores is not None:
                all_redundancy_scores.append(redundancy_scores)
            all_image_ids.extend(image_ids)

    # Concatenate all predictions and labels
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    if all_redundancy_scores:
        all_redundancy_scores = np.concatenate(all_redundancy_scores, axis=0)

    # Debug: Print shapes
    logger.info(f"Predictions shape: {all_preds.shape}")
    logger.info(f"Labels shape: {all_labels.shape}")

    # Ensure both have the same number of classes
    min_classes = min(all_preds.shape[1], all_labels.shape[1])
    all_preds = all_preds[:, :min_classes]
    all_labels = all_labels[:, :min_classes]

    logger.info(f"After adjustment - Predictions shape: {all_preds.shape}, Labels shape: {all_labels.shape}")

    # Compute metrics
    metrics = compute_metrics(all_labels, all_preds, threshold=args.threshold)

    # Log metrics
    logger.info(f"Evaluation Results on {args.split} split:")
    logger.info(f"F1: {metrics['f1']:.4f}")
    logger.info(f"Precision: {metrics['precision']:.4f}")
    logger.info(f"Recall: {metrics['recall']:.4f}")
    logger.info(f"mAP: {metrics['mAP']:.4f}")

    # Save metrics
    with open(os.path.join(args.output_dir, f'{args.split}_metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=2)

    # Save detailed results
    detailed_results = {
        'predictions': all_preds.tolist(),
        'labels': all_labels.tolist(),
        'image_ids': all_image_ids,
        'metrics': metrics
    }

    if all_redundancy_scores:
        detailed_results['redundancy_scores'] = all_redundancy_scores.tolist()

    with open(os.path.join(args.output_dir, f'{args.split}_detailed_results.json'), 'w') as f:
        json.dump(detailed_results, f, indent=2)

    return metrics, all_preds, all_labels, all_redundancy_scores

def generate_visualizations(metrics, preds, labels, output_dir, split):
    """Generate evaluation visualizations."""
    logger.info(f"Generating visualizations for {split} split...")

    # Create visualization directory
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)

    # MIR-Flickr concept names (first 24 concepts used in training)
    concept_names = [
        'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 'flower', 'food', 'indoor',
        'lake', 'male', 'night', 'people', 'plant_life', 'portrait', 'river', 'sea', 'sky', 'structures',
        'sunset', 'transport', 'tree', 'water'
    ]

    # Ensure concept names match the number of classes in predictions/labels
    num_classes = preds.shape[1]
    concept_names = concept_names[:num_classes]

    # 1. Per-class metrics
    per_class_precision = metrics.get('per_class_precision', [])
    per_class_recall = metrics.get('per_class_recall', [])
    per_class_f1 = metrics.get('per_class_f1', [])

    if per_class_precision and per_class_recall and per_class_f1:
        plt.figure(figsize=(15, 8))
        x = np.arange(len(concept_names))
        width = 0.25

        plt.bar(x - width, per_class_precision, width, label='Precision', alpha=0.8)
        plt.bar(x, per_class_recall, width, label='Recall', alpha=0.8)
        plt.bar(x + width, per_class_f1, width, label='F1', alpha=0.8)

        plt.xlabel('Concept')
        plt.ylabel('Score')
        plt.title('Per-Concept Metrics')
        plt.xticks(x, concept_names, rotation=45, ha='right')
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, f'{split}_per_concept_metrics.png'), dpi=300)
        plt.close()

    # 2. Prediction distribution
    plt.figure(figsize=(12, 6))

    # Convert predictions to binary
    binary_preds = (preds > 0.5).astype(int)

    # Count predictions per concept
    pred_counts = np.sum(binary_preds, axis=0)
    label_counts = np.sum(labels, axis=0)

    x = np.arange(len(concept_names))
    width = 0.35

    plt.bar(x - width/2, label_counts, width, label='Ground Truth', alpha=0.8)
    plt.bar(x + width/2, pred_counts, width, label='Predictions', alpha=0.8)

    plt.xlabel('Concept')
    plt.ylabel('Count')
    plt.title('Concept Distribution: Ground Truth vs Predictions')
    plt.xticks(x, concept_names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, f'{split}_concept_distribution.png'), dpi=300)
    plt.close()

    # 3. Confusion matrix (concept co-occurrence)
    plt.figure(figsize=(12, 10))

    # Compute concept co-occurrence in ground truth
    cooccurrence = np.dot(labels.T, labels)

    # Normalize
    cooccurrence_norm = cooccurrence / np.maximum(np.diag(cooccurrence)[:, None], 1)

    sns.heatmap(cooccurrence_norm,
                xticklabels=concept_names,
                yticklabels=concept_names,
                cmap='Blues',
                annot=False,
                fmt='.2f')

    plt.title('Concept Co-occurrence Matrix')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, f'{split}_concept_cooccurrence.png'), dpi=300)
    plt.close()

    logger.info(f"Visualizations saved to {vis_dir}")

def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description="Evaluate KG-Disentangle-Net on MIR-Flickr")

    # Model and data arguments
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to trained model')
    parser.add_argument('--data_path', type=str, default='/home/<USER>/workplace/dwb/data/mirflickr',
                        help='Path to MIR-Flickr dataset')
    parser.add_argument('--kg_path', type=str, default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./mirflickr_evaluation_results',
                        help='Output directory for evaluation results')

    # Evaluation arguments
    parser.add_argument('--split', type=str, default='test', choices=['train', 'val', 'test'],
                        help='Dataset split to evaluate')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary classification')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for evaluation')

    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Load model
    model = load_model(args.model_path, device)

    # Create dataset
    dataset = MIRFlickrDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode=args.split
    )

    # Create data loader
    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Evaluate model
    metrics, preds, labels, redundancy_scores = evaluate_model(model, dataloader, device, args)

    # Generate visualizations
    generate_visualizations(metrics, preds, labels, args.output_dir, args.split)

    logger.info("Evaluation completed successfully")
    logger.info(f"Results saved to: {args.output_dir}")

if __name__ == "__main__":
    main()
